package com.smaile.health.config;

import com.smaile.health.exception.NotFoundException;
import com.smaile.health.exception.ReferencedException;
import com.smaile.health.exception.SmaileAuthenticationException;
import com.smaile.health.exception.SmaileRuntimeException;
import com.smaile.health.exception.SmaileValidationException;
import com.smaile.health.exception.ValidationException;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.authentication.AuthenticationCredentialsNotFoundException;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.CredentialsExpiredException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.context.request.WebRequest;
import org.zalando.problem.Problem;
import org.zalando.problem.ProblemBuilder;
import org.zalando.problem.Status;
import org.zalando.problem.spring.web.advice.ProblemHandling;
import org.zalando.problem.spring.web.advice.security.SecurityAdviceTrait;

import java.net.URI;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Global exception handler for the SMAILE Health application.
 */
@Slf4j
@ControllerAdvice
@RequiredArgsConstructor
public class GlobalErrorHandlingConfig implements ProblemHandling, SecurityAdviceTrait {

    private static final String PROBLEM_BASE_URL = "https://smaile.health/problems/";
    private static final String RUNTIME_ERROR_TYPE = PROBLEM_BASE_URL + "runtime-error";
    private static final String NOT_FOUND_TYPE = PROBLEM_BASE_URL + "not-found";
    private static final String REFERENCED_ENTITY_TYPE = PROBLEM_BASE_URL + "referenced-entity";
    private static final String VALIDATION_ERROR_TYPE = PROBLEM_BASE_URL + "validation-failed";
    private static final String CONSTRAINT_VIOLATION_TYPE = PROBLEM_BASE_URL + "constraint-violation";

    // Authentication & Authorization Problem Types
    private static final String AUTHENTICATION_ERROR_TYPE = PROBLEM_BASE_URL + "authentication-error";
    private static final String AUTHORIZATION_ERROR_TYPE = PROBLEM_BASE_URL + "authorization-error";
    private static final String ACCOUNT_DISABLED_TYPE = PROBLEM_BASE_URL + "account-disabled";
    private static final String ACCOUNT_LOCKED_TYPE = PROBLEM_BASE_URL + "account-locked";
    private static final String ACCOUNT_EXPIRED_TYPE = PROBLEM_BASE_URL + "account-expired";
    private static final String CREDENTIALS_EXPIRED_TYPE = PROBLEM_BASE_URL + "credentials-expired";
    private static final String INSUFFICIENT_AUTHENTICATION_TYPE = PROBLEM_BASE_URL + "insufficient-authentication";

    /**
     * Handles SmaileRuntimeException - general runtime errors in the application.
     */
    @ExceptionHandler(SmaileRuntimeException.class)
    public ResponseEntity<Problem> handleSmaileRuntimeException(
            SmaileRuntimeException exception,
            WebRequest request) {

        log.warn("SmaileRuntimeException occurred: {}", exception.getMessage(), exception);

        Problem problem = createProblemBuilder(RUNTIME_ERROR_TYPE, "Runtime Error", Status.BAD_REQUEST)
                .withDetail(exception.getMessage())
                .build();

        return ResponseEntity.badRequest().body(problem);
    }

    /**
     * Handles NotFoundException - when requested resources cannot be found.
     */
    @ExceptionHandler(NotFoundException.class)
    public ResponseEntity<Problem> handleNotFoundException(
            NotFoundException exception,
            WebRequest request) {

        log.info("NotFoundException occurred: {}", exception.getMessage());

        Problem problem = createProblemBuilder(NOT_FOUND_TYPE, "Resource Not Found", Status.NOT_FOUND)
                .withDetail(exception.getMessage())
                .build();

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(problem);
    }

    /**
     * Handles ReferencedException - when attempting to delete entities that are referenced by others.
     */
    @ExceptionHandler(ReferencedException.class)
    public ResponseEntity<Problem> handleReferencedException(
            ReferencedException exception,
            WebRequest request) {

        log.warn("ReferencedException occurred: {}", exception.getMessage(), exception);

        Problem problem = createProblemBuilder(REFERENCED_ENTITY_TYPE, "Referenced Entity", Status.CONFLICT)
                .withDetail("Cannot delete entity because it is referenced by other entities")
                .with("message", exception.getMessage())
                .build();

        return ResponseEntity.status(HttpStatus.CONFLICT).body(problem);
    }

    /**
     * Handles ValidationException - general validation errors in business logic.
     */
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<Problem> handleValidationException(
            ValidationException exception,
            WebRequest request) {

        log.warn("ValidationException occurred: {}", exception.getMessage(), exception);

        Problem problem = createProblemBuilder(VALIDATION_ERROR_TYPE, "Validation Failed", Status.BAD_REQUEST)
                .withDetail(exception.getMessage())
                .build();

        return ResponseEntity.badRequest().body(problem);
    }

    /**
     * Handles ConstraintViolationException - constraint violations.
     */
    @Override
    public ResponseEntity<Problem> handleConstraintViolation(
            ConstraintViolationException exception,
            NativeWebRequest request) {

        log.warn("ConstraintViolationException occurred: {} violations", exception.getConstraintViolations().size(),
                exception);

        Map<String, String> violations = exception.getConstraintViolations().stream()
                .collect(Collectors.toMap(
                        violation -> violation.getPropertyPath().toString(),
                        ConstraintViolation::getMessage,
                        (existing, replacement) -> existing // Handle duplicate keys
                ));

        Problem problem = createProblemBuilder(CONSTRAINT_VIOLATION_TYPE, "Constraint Violation", Status.BAD_REQUEST)
                .with("violations", violations)
                .build();

        return ResponseEntity.badRequest().body(problem);
    }

    /**
     * Handles MethodArgumentNotValidException - Spring MVC validation failures.
     */
    @Override
    public ResponseEntity<Problem> handleMethodArgumentNotValid(
            MethodArgumentNotValidException exception,
            NativeWebRequest request) {

        log.warn("MethodArgumentNotValidException occurred: {} field errors", exception.getFieldErrors().size(),
                exception);

        Map<String, String> fieldErrors = exception.getFieldErrors().stream()
                .collect(Collectors.toMap(
                        FieldError::getField,
                        error -> error.getDefaultMessage() != null ? error.getDefaultMessage() : "Invalid value",
                        (existing, replacement) -> existing // Handle duplicate keys
                ));

        Problem problem = createProblemBuilder(VALIDATION_ERROR_TYPE, "Validation Failed", Status.BAD_REQUEST)
                .with("fieldErrors", fieldErrors)
                .build();

        return ResponseEntity.badRequest().body(problem);
    }

    /**
     * Handles SmaileValidationException - custom validation errors with structured error maps.
     */
    @ExceptionHandler(SmaileValidationException.class)
    public ResponseEntity<Problem> handleSmaileValidationException(
            SmaileValidationException exception,
            WebRequest request) {

        log.warn("SmaileValidationException occurred: {} errors", exception.getErrorMap().size(), exception);

        Problem problem = createProblemBuilder(VALIDATION_ERROR_TYPE, "Validation Failed", Status.BAD_REQUEST)
                .with("errors", exception.getErrorMap())
                .build();

        return ResponseEntity.badRequest().body(problem);
    }

    // ========================================
    // AUTHENTICATION EXCEPTION HANDLERS
    // ========================================

    /**
     * Handles BadCredentialsException - invalid username/password or authentication credentials.
     */
    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<Problem> handleBadCredentialsException(
            BadCredentialsException exception,
            WebRequest request) {

        log.warn("BadCredentialsException occurred: {}", exception.getMessage());

        Problem problem = createProblemBuilder(AUTHENTICATION_ERROR_TYPE, "Authentication Failed", Status.UNAUTHORIZED)
                .withDetail("Invalid credentials provided")
                .with("errorCode", "INVALID_CREDENTIALS")
                .build();

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(problem);
    }

    /**
     * Handles DisabledException - user account is disabled.
     */
    @ExceptionHandler(DisabledException.class)
    public ResponseEntity<Problem> handleDisabledException(
            DisabledException exception,
            WebRequest request) {

        log.warn("DisabledException occurred: {}", exception.getMessage());

        Problem problem = createProblemBuilder(ACCOUNT_DISABLED_TYPE, "Account Disabled", Status.UNAUTHORIZED)
                .withDetail("User account is disabled")
                .with("errorCode", "ACCOUNT_DISABLED")
                .build();

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(problem);
    }

    /**
     * Handles LockedException - user account is locked.
     */
    @ExceptionHandler(LockedException.class)
    public ResponseEntity<Problem> handleLockedException(
            LockedException exception,
            WebRequest request) {

        log.warn("LockedException occurred: {}", exception.getMessage());

        Problem problem = createProblemBuilder(ACCOUNT_LOCKED_TYPE, "Account Locked", Status.UNAUTHORIZED)
                .withDetail("User account is locked")
                .with("errorCode", "ACCOUNT_LOCKED")
                .build();

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(problem);
    }

    /**
     * Handles AccountExpiredException - user account has expired.
     */
    @ExceptionHandler(AccountExpiredException.class)
    public ResponseEntity<Problem> handleAccountExpiredException(
            AccountExpiredException exception,
            WebRequest request) {

        log.warn("AccountExpiredException occurred: {}", exception.getMessage());

        Problem problem = createProblemBuilder(ACCOUNT_EXPIRED_TYPE, "Account Expired", Status.UNAUTHORIZED)
                .withDetail("User account has expired")
                .with("errorCode", "ACCOUNT_EXPIRED")
                .build();

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(problem);
    }

    /**
     * Handles CredentialsExpiredException - user credentials have expired.
     */
    @ExceptionHandler(CredentialsExpiredException.class)
    public ResponseEntity<Problem> handleCredentialsExpiredException(
            CredentialsExpiredException exception,
            WebRequest request) {

        log.warn("CredentialsExpiredException occurred: {}", exception.getMessage());

        Problem problem = createProblemBuilder(CREDENTIALS_EXPIRED_TYPE, "Credentials Expired", Status.UNAUTHORIZED)
                .withDetail("User credentials have expired")
                .with("errorCode", "CREDENTIALS_EXPIRED")
                .build();

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(problem);
    }

    /**
     * Handles InsufficientAuthenticationException - insufficient authentication for the requested operation.
     */
    @ExceptionHandler(InsufficientAuthenticationException.class)
    public ResponseEntity<Problem> handleInsufficientAuthenticationException(
            InsufficientAuthenticationException exception,
            WebRequest request) {

        log.warn("InsufficientAuthenticationException occurred: {}", exception.getMessage());

        Problem problem = createProblemBuilder(INSUFFICIENT_AUTHENTICATION_TYPE, "Insufficient Authentication", Status.UNAUTHORIZED)
                .withDetail("Insufficient authentication for the requested operation")
                .with("errorCode", "INSUFFICIENT_AUTHENTICATION")
                .build();

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(problem);
    }

    /**
     * Handles AuthenticationCredentialsNotFoundException - authentication credentials not found.
     */
    @ExceptionHandler(AuthenticationCredentialsNotFoundException.class)
    public ResponseEntity<Problem> handleAuthenticationCredentialsNotFoundException(
            AuthenticationCredentialsNotFoundException exception,
            WebRequest request) {

        log.warn("AuthenticationCredentialsNotFoundException occurred: {}", exception.getMessage());

        Problem problem = createProblemBuilder(AUTHENTICATION_ERROR_TYPE, "Authentication Credentials Not Found", Status.UNAUTHORIZED)
                .withDetail("Authentication credentials not found")
                .with("errorCode", "CREDENTIALS_NOT_FOUND")
                .build();

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(problem);
    }

    /**
     * Handles UsernameNotFoundException - username not found during authentication.
     */
    @ExceptionHandler(UsernameNotFoundException.class)
    public ResponseEntity<Problem> handleUsernameNotFoundException(
            UsernameNotFoundException exception,
            WebRequest request) {

        log.warn("UsernameNotFoundException occurred: {}", exception.getMessage());

        Problem problem = createProblemBuilder(AUTHENTICATION_ERROR_TYPE, "User Not Found", Status.UNAUTHORIZED)
                .withDetail("User not found")
                .with("errorCode", "USER_NOT_FOUND")
                .build();

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(problem);
    }

    /**
     * Handles AuthenticationServiceException - authentication service errors.
     */
    @ExceptionHandler(AuthenticationServiceException.class)
    public ResponseEntity<Problem> handleAuthenticationServiceException(
            AuthenticationServiceException exception,
            WebRequest request) {

        log.error("AuthenticationServiceException occurred: {}", exception.getMessage(), exception);

        Problem problem = createProblemBuilder(AUTHENTICATION_ERROR_TYPE, "Authentication Service Error", Status.UNAUTHORIZED)
                .withDetail("Authentication service temporarily unavailable")
                .with("errorCode", "AUTHENTICATION_SERVICE_ERROR")
                .build();

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(problem);
    }

    /**
     * Handles SmaileAuthenticationException - custom authentication errors specific to SMAILE platform.
     */
    @ExceptionHandler(SmaileAuthenticationException.class)
    public ResponseEntity<Problem> handleSmaileAuthenticationException(
            SmaileAuthenticationException exception,
            WebRequest request) {

        log.warn("SmaileAuthenticationException occurred: {}", exception.getMessage(), exception);

        Problem problem = createProblemBuilder(AUTHENTICATION_ERROR_TYPE, "SMAILE Authentication Error", Status.UNAUTHORIZED)
                .withDetail(exception.getMessage())
                .with("errorCode", "SMAILE_AUTHENTICATION_ERROR")
                .build();

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(problem);
    }

    /**
     * Handles generic AuthenticationException - fallback for any other authentication errors.
     */
    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<Problem> handleAuthenticationException(
            AuthenticationException exception,
            WebRequest request) {

        log.warn("AuthenticationException occurred: {}", exception.getMessage(), exception);

        Problem problem = createProblemBuilder(AUTHENTICATION_ERROR_TYPE, "Authentication Failed", Status.UNAUTHORIZED)
                .withDetail("Authentication failed")
                .with("errorCode", "AUTHENTICATION_FAILED")
                .build();

        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(problem);
    }

    // ========================================
    // AUTHORIZATION EXCEPTION HANDLERS
    // ========================================

    /**
     * Handles AccessDeniedException - user lacks sufficient permissions for the requested operation.
     */
    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<Problem> handleAccessDeniedException(
            AccessDeniedException exception,
            WebRequest request) {

        log.warn("AccessDeniedException occurred: {}", exception.getMessage());

        Problem problem = createProblemBuilder(AUTHORIZATION_ERROR_TYPE, "Access Denied", Status.FORBIDDEN)
                .withDetail("Insufficient permissions for the requested operation")
                .with("errorCode", "ACCESS_DENIED")
                .with("message", exception.getMessage())
                .build();

        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(problem);
    }

    /**
     * Creates a standardized ProblemBuilder with common configuration.
     *
     * @param type   The problem type URI
     * @param title  The problem title
     * @param status The HTTP status
     * @return A configured ProblemBuilder
     */
    private ProblemBuilder createProblemBuilder(String type, String title, Status status) {
        return Problem.builder()
                .withType(URI.create(type))
                .withTitle(title)
                .withStatus(status);
    }

    /**
     * Handles any unexpected exceptions that aren't caught by specific handlers.
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Problem> handleGenericException(Exception exception, WebRequest request) {
        log.error("Unexpected exception occurred : {}", exception.getMessage(), exception);
        String userMessage ="An unexpected error occurred. Please contact support";

        Problem problem = createProblemBuilder(
                PROBLEM_BASE_URL + "internal-error",
                "Internal Server Error",
                Status.INTERNAL_SERVER_ERROR)
                .withDetail(userMessage)
                .build();

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(problem);
    }

}