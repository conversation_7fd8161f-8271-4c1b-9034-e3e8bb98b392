package com.smaile.health.config;

import com.smaile.health.security.authentication.SmaileAuthenticationFilter;
import com.smaile.health.security.authentication.SmaileAuthenticationProvider;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Profile;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.zalando.problem.Problem;
import org.zalando.problem.Status;
import org.zalando.problem.spring.web.advice.security.SecurityProblemSupport;

/**
 * Web Security Configuration for SMAILE Health platform.
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025/08/26
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity(prePostEnabled = true, securedEnabled = true)
@Profile("!test")
@RequiredArgsConstructor
@Import(SecurityProblemSupport.class)
public class WebSecurityConfig {

    private final SmaileAuthenticationProvider authenticationProvider;
    private final SmaileAuthenticationFilter authenticationFilter;
    private final SecurityProblemSupport problemSupport;

    public static final String[] WHITELIST = {
            "/v3/api-docs/**",
            "/swagger-ui/**",
            "/utilities/**",
            "/register/**",
            "/actuator/**"
    };

    /**
     * Configures the authentication manager with custom authentication provider.
     *
     * @return AuthenticationManager configured with SmaileAuthenticationProvider
     */
    @Bean
    public AuthenticationManager authenticationManager() {
        return new ProviderManager(authenticationProvider);
    }

    /**
     * Configures the main security filter chain.
     *
     * @param http HttpSecurity configuration
     * @return SecurityFilterChain configured for SMAILE Health
     * @throws Exception if configuration fails
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
                .cors(AbstractHttpConfigurer::disable)
                .csrf(AbstractHttpConfigurer::disable)

                // Configure authn/authz exception handling
                .exceptionHandling(exceptionHandling -> exceptionHandling
                        .authenticationEntryPoint(problemSupport)
                        .accessDeniedHandler(problemSupport))

                // Add custom authentication filter
                .addFilterBefore(authenticationFilter, UsernamePasswordAuthenticationFilter.class)

                .authorizeHttpRequests(authorize -> authorize
                        .requestMatchers(WHITELIST).permitAll()
                        .anyRequest().authenticated()
                )

                .sessionManagement(session -> session
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                );

        return http.build();
    }

    @Bean
    public AuthenticationEntryPoint authenticationEntryPoint() {
        return (HttpServletRequest request, HttpServletResponse response,
                AuthenticationException authException) -> {

            response.setContentType("application/problem+json");
            response.setStatus(Status.UNAUTHORIZED.getStatusCode());

            Problem problem = Problem.builder()
                    .withTitle("Unauthorized")
                    .withDetail(authException.getMessage())
                    .withStatus(Status.UNAUTHORIZED)
                    .build();

            response.getWriter().write(problem.toString());
        };
    }

    @Bean
    public AccessDeniedHandler accessDeniedHandler() {
        return (HttpServletRequest request, HttpServletResponse response,
                AccessDeniedException accessDeniedException) -> {

            response.setContentType("application/problem+json");
            response.setStatus(Status.FORBIDDEN.getStatusCode());

            Problem problem = Problem.builder()
                    .withTitle("Forbidden")
                    .withDetail(accessDeniedException.getMessage())
                    .withStatus(Status.FORBIDDEN)
                    .build();

            response.getWriter().write(problem.toString());
        };
    }

}
